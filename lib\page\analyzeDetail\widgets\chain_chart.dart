import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';

import '../../../common/model/analyze_details_model/alarms_statistic.dart';
import '../../../common/widget/corner_card.dart';
import '../controller.dart';

class ChainChart extends StatelessWidget {
  const ChainChart({super.key, required this.controller});

  final AnalyzedetailController controller;

  @override
  Widget build(BuildContext context) {
    List<AlarmsStatistic> datas = controller.details.alarmsStatistics!;
    List<AlarmsStatistic> chain = controller.chain.value.alarmsStatistics!;

    return CornerCard(
      height: 300,
      child: Column(
        children: [
          const Text(
            '警情环比',
            style: TextStyle(fontSize: 25, color: Colors.white),
          ),
          Expanded(
            child: charts.BarChart(
              _createSeriesList(datas, chain),
              animate: true,

              domainAxis: charts.OrdinalAxisSpec(
                renderSpec: charts.SmallTickRendererSpec(
                  labelStyle: charts.TextStyleSpec(
                      color: charts.Color.fromHex(code: '#ffffff')),
                ),
              ),
              // 自定义柱形渲染器来控制宽度
              defaultRenderer: charts.BarRendererConfig(
                // 设置柱形组的宽度比例 (0.0 到 1.0)
                groupingType: charts.BarGroupingType.grouped,
                // 柱形组内柱形之间的间距
                barGroupInnerPaddingPx: 4,
                // 自定义柱形宽度 - 通过设置最大柱形宽度
                maxBarWidthPx: 30, // 每个柱形的最大宽度为30像素
                // 显示标签
                barRendererDecorator: charts.BarLabelDecorator<String>(
                  labelAnchor: charts.BarLabelAnchor.end,
                  labelPosition: charts.BarLabelPosition.outside,
                  insideLabelStyleSpec: charts.TextStyleSpec(
                    color: charts.Color.fromHex(code: '#ffffff'),
                    fontSize: 12,
                  ),
                  outsideLabelStyleSpec: charts.TextStyleSpec(
                    color: charts.Color.fromHex(code: '#ffffff'),
                    fontSize: 12,
                  ),
                ),
              ),
              behaviors: [
                // 添加图例
                charts.SeriesLegend(
                  position: charts.BehaviorPosition.top,
                  horizontalFirst: false,
                  desiredMaxRows: 1,
                  cellPadding: const EdgeInsets.only(right: 4.0, bottom: 4.0),
                  entryTextStyle: charts.TextStyleSpec(
                    color: charts.Color.fromHex(code: '#ffffff'),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 创建自定义宽度的柱形图系列
  List<charts.Series<AlarmsStatistic, String>> _createSeriesList(
      List<AlarmsStatistic> datas, List<AlarmsStatistic> chain) {
    double percent = 0;

    // 计算本周期比上周期上升/下降的百分比
    for (var i = 0; i < datas.length; i++) {
      if (chain[i].alarmCount != 0) {
        percent = (datas[i].alarmCount - chain[i].alarmCount) /
            chain[i].alarmCount *
            100;
        datas[i].percent = percent;
      }
    }

    return [
      charts.Series<AlarmsStatistic, String>(
        id: '上周期',
        domainFn: (AlarmsStatistic data, _) => data.name ?? '',
        measureFn: (AlarmsStatistic data, _) => data.alarmCount,
        data: chain,
        labelAccessorFn: (AlarmsStatistic datum, int? index) =>
            '${datum.alarmCount}',
        colorFn: (_, __) => charts.MaterialPalette.cyan.shadeDefault,
        strokeWidthPxFn: (_, __) => 1.0,
      ),
      charts.Series<AlarmsStatistic, String>(
        id: '本周期',
        domainFn: (AlarmsStatistic data, _) => data.name ?? '',
        measureFn: (AlarmsStatistic data, _) => data.alarmCount,
        data: datas,
        labelAccessorFn: (AlarmsStatistic datum, int? index) =>
            percent.toString(),
        colorFn: (_, __) => charts.MaterialPalette.blue.shadeDefault,
        // 自定义柱形宽度 - 通过设置strokeWidth来控制边框
        strokeWidthPxFn: (_, __) => 1.0,
      ),
    ];
  }
}
